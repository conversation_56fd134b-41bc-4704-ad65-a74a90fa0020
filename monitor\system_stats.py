import psutil

def get_cpu_usage():
    return psutil.cpu_percent(percpu=True)

def get_ram_usage():
    mem = psutil.virtual_memory()
    return mem.used, mem.total

def get_disk_usage():
    disk = psutil.disk_usage('/')
    return disk.used, disk.total

def get_network_usage():
    net = psutil.net_io_counters()
    return net.bytes_sent, net.bytes_recv

def get_process_list(limit=20):
    procs = [(p.info['pid'], p.info['name'], p.info['cpu_percent'], p.info['memory_percent'])
             for p in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent'])]
    # Sort by CPU usage descending
    procs.sort(key=lambda x: x[2], reverse=True)
    return procs[:limit]
