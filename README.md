# Resource Monitor (Linux Pop!_OS)

A beautiful, modern resource monitor for Linux (Pop!_OS/Ubuntu), built with Python, PyQt6, and psutil. 

## Features
- Real-time CPU, RAM, Disk, and Network monitoring
- Live process list
- Modern, themeable GUI
- Easy to install and run

## Quick Start

1. Install dependencies:
    ```bash
    pip install -r requirements.txt
    ```
2. Run the app:
    ```bash
    python resource_monitor.py
    ```

## Project Structure
```
learn_10/
├── gui/
│   ├── __init__.py
│   └── main_window.py
├── monitor/
│   ├── __init__.py
│   └── system_stats.py
├── assets/
├── resource_monitor.py
├── requirements.txt
└── README.md
```

## Dependencies
- PyQt6
- psutil
- pyqtgraph

---

This project is a starting point. Future enhancements will include more detailed process info, theming, and packaging for AppImage or .deb.
