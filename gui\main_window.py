from PyQt6 import QtWidgets, QtGui, QtCore
from pyqtgraph import PlotWidget, plot
import pyqtgraph as pg
from monitor import system_stats

class MainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Resource Monitor")
        self.setMinimumSize(900, 600)
        self.setWindowIcon(QtGui.QIcon())  # Add icon path later
        self._setup_ui()
        self._init_data()
        self._start_timer()

    def _setup_ui(self):
        # Central widget
        central = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout(central)

        # Sidebar for process list
        self.process_list = QtWidgets.QListWidget()
        self.process_list.setFixedWidth(220)
        self.process_list.setStyleSheet("background: #232629; color: #f8f8f2;")
        layout.addWidget(self.process_list)

        # Main stats area
        stats_layout = QtWidgets.QVBoxLayout()
        grid = QtWidgets.QGridLayout()
        # Panels with plots
        self.cpu_panel, self.cpu_plot = self._make_panel("CPU Usage")
        self.ram_panel, self.ram_plot = self._make_panel("RAM Usage")
        self.disk_panel, self.disk_plot = self._make_panel("Disk Usage")
        self.net_panel, self.net_plot = self._make_panel("Network Usage")
        grid.addWidget(self.cpu_panel, 0, 0)
        grid.addWidget(self.ram_panel, 0, 1)
        grid.addWidget(self.disk_panel, 1, 0)
        grid.addWidget(self.net_panel, 1, 1)
        stats_layout.addLayout(grid)
        layout.addLayout(stats_layout)
        self.setCentralWidget(central)

    def _make_panel(self, title):
        box = QtWidgets.QGroupBox(title)
        vbox = QtWidgets.QVBoxLayout()
        plot = PlotWidget()
        plot.setBackground('w')
        vbox.addWidget(plot)
        box.setLayout(vbox)
        return box, plot

    def _init_data(self):
        self.cpu_history = [[] for _ in range(len(system_stats.get_cpu_usage()))]
        self.ram_history = []
        self.disk_history = []
        self.net_sent_history = []
        self.net_recv_history = []
        self.max_points = 60  # 1 minute at 1Hz
        # For network delta
        self._last_net = system_stats.get_network_usage()
        # Plot curves
        self.cpu_curves = [self.cpu_plot.plot(pen=pg.mkPen(color=(i*60, 150, 255, 200), width=2)) for i in range(len(self.cpu_history))]
        self.ram_curve = self.ram_plot.plot(pen=pg.mkPen('g', width=2))
        self.disk_curve = self.disk_plot.plot(pen=pg.mkPen('m', width=2))
        self.net_plot.addLegend()
        self.net_sent_curve = self.net_plot.plot(pen=pg.mkPen('b', width=2), name='Sent')
        self.net_recv_curve = self.net_plot.plot(pen=pg.mkPen('r', width=2), name='Recv')

    def _start_timer(self):
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self._update_stats)
        self.timer.start(1000)  # 1Hz

    def _update_stats(self):
        # CPU
        cpu = system_stats.get_cpu_usage()
        for i, val in enumerate(cpu):
            self.cpu_history[i].append(val)
            if len(self.cpu_history[i]) > self.max_points:
                self.cpu_history[i].pop(0)
            self.cpu_curves[i].setData(self.cpu_history[i])
        # RAM
        used, total = system_stats.get_ram_usage()
        percent = 100 * used / total
        self.ram_history.append(percent)
        if len(self.ram_history) > self.max_points:
            self.ram_history.pop(0)
        self.ram_curve.setData(self.ram_history)
        # Disk
        dused, dtotal = system_stats.get_disk_usage()
        dpercent = 100 * dused / dtotal
        self.disk_history.append(dpercent)
        if len(self.disk_history) > self.max_points:
            self.disk_history.pop(0)
        self.disk_curve.setData(self.disk_history)
        # Network
        sent, recv = system_stats.get_network_usage()
        last_sent, last_recv = self._last_net
        sent_delta = (sent - last_sent) / 1024  # KB/s
        recv_delta = (recv - last_recv) / 1024
        self.net_sent_history.append(sent_delta)
        self.net_recv_history.append(recv_delta)
        if len(self.net_sent_history) > self.max_points:
            self.net_sent_history.pop(0)
        if len(self.net_recv_history) > self.max_points:
            self.net_recv_history.pop(0)
        self.net_sent_curve.setData(self.net_sent_history)
        self.net_recv_curve.setData(self.net_recv_history)
        self._last_net = (sent, recv)
        # Process list
        self.process_list.clear()
        procs = system_stats.get_process_list()
        for pid, name, cpu, mem in procs:
            self.process_list.addItem(f"{pid:5d} {name[:18]:18}  CPU: {cpu:5.1f}%  MEM: {mem:4.1f}%")
