# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture

This is a Python PyQt6-based resource monitor application with the following structure:

- `resource_monitor.py` - Main entry point that launches the PyQt6 application
- `gui/main_window.py` - Main window implementation with real-time plots and process list
- `monitor/system_stats.py` - System monitoring utilities using psutil
- `main.py` - Simple CLI script (unrelated to main app)

The GUI uses PyQt6 with pyqtgraph for real-time plotting of CPU, RAM, disk, and network usage. The monitor module abstracts psutil calls for system statistics.

## Common Commands

Install dependencies:
```bash
pip install -r requirements.txt
```

Run the resource monitor GUI:
```bash
python3 resource_monitor.py
```

Run the simple CLI script:
```bash
python3 main.py
```

## Development Notes

- Use `python3` command instead of `python` (WSL environment)
- The application updates stats every 1 second (1Hz) and maintains 60 data points (1 minute history)
- Network usage is calculated as delta (KB/s) between measurements
- Process list shows top 20 processes sorted by CPU usage